/*!
    \file    main.c
    \brief   Quadrant Sensor Project Main File

    \version 2025-07-26, V1.0.0, firmware for GD32F3x0 Quadrant Sensor
*/

/*
    Copyright (c) 2025, GigaDevice Semiconductor Inc.

    Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice,
       this list of conditions and the following disclaimer in the documentation
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors
       may be used to endorse or promote products derived from this software without
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
*/

#include "gd32f3x0.h"
#include "systick.h"
#include <stdio.h>
#include "main.h"
#include "cm1103_driver.h"
#include "soft_i2c.h"
#include "rs485_comm.h"

/* External variables from rs485_comm.c */
extern volatile rs485_rx_state_t g_rs485_rx_state;
extern volatile bool g_rs485_frame_received;
extern volatile bool g_rs485_tx_complete;

/*!
    \brief      initialize system clock
    \param[in]  none
    \param[out] none
    \retval     none
*/
void system_clock_config(void)
{
    /* enable external high speed oscillator (HXTAL) - 8MHz crystal */
    rcu_osci_on(RCU_HXTAL);
    rcu_osci_stab_wait(RCU_HXTAL);

    /* configure PLL preselection to use HXTAL */
    rcu_pll_preselection_config(RCU_PLLPRESEL_HXTAL);

    /* configure PLL: 8MHz HXTAL * 9 = 72MHz */
    rcu_pll_config(RCU_PLLSRC_HXTAL_IRC48M, RCU_PLL_MUL9);

    /* enable PLL */
    rcu_osci_on(RCU_PLL_CK);
    rcu_osci_stab_wait(RCU_PLL_CK);

    /* configure AHB prescaler: SYSCLK not divided */
    rcu_ahb_clock_config(RCU_AHB_CKSYS_DIV1);

    /* configure APB1 prescaler: HCLK divided by 2 */
    rcu_apb1_clock_config(RCU_APB1_CKAHB_DIV2);

    /* configure APB2 prescaler: HCLK not divided */
    rcu_apb2_clock_config(RCU_APB2_CKAHB_DIV1);

    /* select PLL as system clock source */
    rcu_system_clock_source_config(RCU_CKSYSSRC_PLL);
    while(RCU_SCSS_PLL != rcu_system_clock_source_get());
}

/*!
    \brief      initialize board peripherals - simplified version
    \param[in]  none
    \param[out] none
    \retval     none
*/
void board_init(void)
{
    /* initialize CM1103 quadrant sensor */
    if(!cm1103_init()) {
        /* CM1103初始化失败，系统无法正常工作 */
        while(1) {
            /* 进入死循环，等待复位 */
            delay_1ms(1000);
        }
    }

    /* 等待传感器稳定 */
    delay_1ms(100);
}

/*!
    \brief      main function - simplified version
    \param[in]  none
    \param[out] none
    \retval     none
*/
int main(void)
{
    /* configure system clock to 72MHz */
    system_clock_config();

    /* configure systick timer */
    systick_config();

    /* initialize RS485 communication */
    if(!rs485_init()) {
        /* RS485初始化失败，发送错误码并进入死循环 */
        while(1) {
            delay_1ms(1000);
            /* 可以在这里添加LED指示或其他错误反馈 */
        }
    }

    /* initialize board peripherals */
    board_init();

    /* 初始化成功指示 - 可以添加LED闪烁等 */

    /* system initialization completed, send ready notification */
    delay_1ms(100);  /* wait for system stabilization */
    rs485_send_system_ready();

    /* main loop - continuous data acquisition and transmission */
    uint32_t last_send_time = 0;
    const uint32_t send_interval_ms = 100;  /* 每100ms发送一次数据 */

    while(1) {
        /* update CM1103 continuous conversion data */
        cm1103_update_continuous_data();

        /* 调试：每1秒检查一次缓存状态 */
        static uint32_t last_debug_time = 0;
        if((systick_get_tick() - last_debug_time) >= 1000) {
            cm1103_get_cache_status();  /* 在此函数设置断点查看缓存状态 */
            last_debug_time = systick_get_tick();
        }

        /* process received RS485 commands (if any) */
        if(g_rs485_frame_received) {
            uint8_t command = rs485_get_received_command();
            rs485_process_command(command);
            g_rs485_frame_received = false;
            g_rs485_rx_state = RX_IDLE;
        }

        /* continuous data transmission */
        uint32_t current_time = systick_get_tick();
        uint32_t time_diff = current_time - last_send_time;  /* 无符号减法自动处理溢出 */
        if(time_diff >= send_interval_ms) {
            /* read cached CM1103 data and send via RS485 */
            cm1103_quadrant_data_t quad_data;

            if(cm1103_read_all_channels(&quad_data)) {
                /* 发送缓存的数据 */
                rs485_send_quadrant_data_auto(&quad_data);
            } else {
                /* 数据读取失败，发送具体错误码 */
                if(!cm1103_is_cached_data_valid()) {
                    rs485_send_error_response(CM1103_ERROR_DATA_EXPIRED);  /* 数据过期 */
                } else {
                    rs485_send_error_response(CM1103_ERROR_I2C_COMM);     /* I2C通信错误 */
                }
            }

            last_send_time = current_time;
        }

        /* 动态延时：根据下次需要执行的时间计算延时 */
        uint32_t next_send_time = send_interval_ms - time_diff;
        if(next_send_time > 10) {
            delay_1ms(5);  /* 较长间隔时延时5ms */
        } else {
            delay_1ms(1);  /* 接近发送时间时延时1ms */
        }
    }
}


