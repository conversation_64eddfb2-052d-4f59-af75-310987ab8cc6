/*!
    \file    cm1103_driver.h
    \brief   CM1103 Quadrant Sensor Driver for GD32F310F8P6

    \version 2025-07-26, V1.0.0, CM1103 driver for GD32F310F8P6
*/

#ifndef CM1103_DRIVER_H
#define CM1103_DRIVER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "gd32f3x0.h"
#include "gd32f3x0_exti.h"
#include "gd32f3x0_syscfg.h"
#include "soft_i2c.h"
#include <stdbool.h>

/* CM1103 设备配置 */
#define CM1103_DEVICE_ADDR      0x48    /* CM1103 I2C设备地址 (7-bit) */
#define CM1103_MAX_CHANNELS     4       /* 四象限传感器通道数 */

/* CM1103 寄存器地址 - 根据数据手册仅有4个寄存器 */
#define CM1103_REG_CONVERSION   0x00    /* 转换结果寄存器 */
#define CM1103_REG_CONFIG       0x01    /* 配置寄存器 */
#define CM1103_REG_LO_THRESH    0x02    /* 低阈值寄存器 */
#define CM1103_REG_HI_THRESH    0x03    /* 高阈值寄存器 */

/* CM1103 配置寄存器位定义 */
#define CM1103_CONFIG_OS        0x8000  /* 操作状态位 */
#define CM1103_CONFIG_MUX_MASK  0x7000  /* 输入多路复用器配置 */
#define CM1103_CONFIG_MUX_AIN0  0x0000  /* AIN0 vs GND (MUX=000) - 修正后的值 */
#define CM1103_CONFIG_MUX_AIN1  0x1000  /* AIN1 vs GND (MUX=001) - 修正后的值 */
#define CM1103_CONFIG_MUX_AIN2  0x2000  /* AIN2 vs GND (MUX=010) - 修正后的值 */
#define CM1103_CONFIG_MUX_AIN3  0x3000  /* AIN3 vs GND (MUX=011) - 修正后的值 */
#define CM1103_CONFIG_PGA_MASK  0x0E00  /* 可编程增益放大器配置 */
#define CM1103_CONFIG_PGA_6144  0x0000  /* ±6.144V */
#define CM1103_CONFIG_PGA_4096  0x0200  /* ±4.096V */
#define CM1103_CONFIG_PGA_2048  0x0400  /* ±2.048V (默认) */
#define CM1103_CONFIG_PGA_1024  0x0600  /* ±1.024V */
#define CM1103_CONFIG_PGA_0512  0x0800  /* ±0.512V */
#define CM1103_CONFIG_PGA_0256  0x0A00  /* ±0.256V */
#define CM1103_CONFIG_MODE      0x0100  /* 设备操作模式 */
#define CM1103_CONFIG_DR_MASK   0x00E0  /* 数据速率 */
#define CM1103_CONFIG_DR_6_25   0x0000  /* 6.25 SPS */
#define CM1103_CONFIG_DR_12_5   0x0020  /* 12.5 SPS */
#define CM1103_CONFIG_DR_25     0x0040  /* 25 SPS */
#define CM1103_CONFIG_DR_50     0x0060  /* 50 SPS */
#define CM1103_CONFIG_DR_100    0x0080  /* 100 SPS (默认) */
#define CM1103_CONFIG_DR_400    0x00A0  /* 400 SPS */
#define CM1103_CONFIG_DR_1000   0x00C0  /* 1000 SPS */
#define CM1103_CONFIG_DR_2000   0x00E0  /* 2000 SPS */
#define CM1103_CONFIG_CMODE     0x0010  /* 比较器模式 */
#define CM1103_CONFIG_CPOL      0x0008  /* 比较器极性 */
#define CM1103_CONFIG_CLAT      0x0004  /* 比较器锁存 */
#define CM1103_CONFIG_CQUE_MASK 0x0003  /* 比较器队列 */

/* MUX配置 - 输入多路复用器（已修正的值） */
/* 根据参考代码分析和数据手册，MUX位定义：000=AIN0, 001=AIN1, 010=AIN2, 011=AIN3 */
/* 注意：之前使用的100~111配置可能导致通道切换异常 */

/* 默认配置值 - 单次转换模式，单端输入，禁用比较器 */
/* 单端输入模式：AIN0相对于GND，量程±4.096V */
/* 参考官方代码使用单次转换模式，避免连续模式的时序复杂性 */
#define CM1103_CONFIG_DEFAULT   (CM1103_CONFIG_PGA_4096 | \
                                CM1103_CONFIG_MODE | \
                                CM1103_CONFIG_DR_100 | \
                                0x0003)
/* CM1103_CONFIG_MODE=0x0100表示MODE=1(单次转换)，COMP_QUE=11禁用比较器(高阻态) */

/* DRDY引脚说明 - CM1103的RDY引脚通过上拉电阻连接到3.3V
 * 由于硬件设计中RDY引脚直接上拉到3.3V，不连接到微控制器GPIO
 * 因此不使用DRDY中断功能，改用轮询方式读取数据
 */

/* 数据结构定义 */
typedef struct {
    uint16_t quadrant_a_raw;    /* 象限A ADC原始值 */
    uint16_t quadrant_b_raw;    /* 象限B ADC原始值 */
    uint16_t quadrant_c_raw;    /* 象限C ADC原始值 */
    uint16_t quadrant_d_raw;    /* 象限D ADC原始值 */
    float quadrant_a_voltage;   /* 象限A 电压值(V) */
    float quadrant_b_voltage;   /* 象限B 电压值(V) */
    float quadrant_c_voltage;   /* 象限C 电压值(V) */
    float quadrant_d_voltage;   /* 象限D 电压值(V) */
    uint32_t timestamp;         /* 采样时间戳 */
    bool valid;                 /* 数据有效标志 */
} cm1103_quadrant_data_t;

typedef struct {
    uint16_t reg_addr;      /* 寄存器地址 */
    uint16_t reg_write;     /* 写入值 */
    uint16_t reg_read;      /* 读取值 */
    char desc[32];          /* 描述 */
} cm1103_register_t;

typedef enum {
    CM1103_MODE_CONTINUOUS = 0, /* 连续转换模式 (MODE位=0) */
    CM1103_MODE_SINGLE,         /* 单次转换模式 (MODE位=1) */
    CM1103_MODE_POWER_DOWN      /* 掉电模式 */
} cm1103_mode_t;

typedef enum {
    CM1103_CHANNEL_A = 0,       /* 象限A (AIN0) */
    CM1103_CHANNEL_B,           /* 象限B (AIN1) */
    CM1103_CHANNEL_C,           /* 象限C (AIN2) */
    CM1103_CHANNEL_D            /* 象限D (AIN3) */
} cm1103_channel_t;

/* 最大通道数 */
#define CM1103_MAX_CHANNELS 4

/* 错误码定义 */
#define CM1103_ERROR_I2C_COMM       0x01    /* I2C通信错误 */
#define CM1103_ERROR_DATA_EXPIRED   0x02    /* 数据过期 */
#define CM1103_ERROR_CONFIG_FAIL    0x03    /* 配置失败 */
#define CM1103_ERROR_INIT_FAIL      0x04    /* 初始化失败 */



/* 状态标志 */
extern volatile bool g_cm1103_data_ready;
extern volatile bool g_cm1103_conversion_done;

/* 缓存数据变量 - 全局可见 */
extern volatile uint16_t g_cached_channel_data[4];  /* 四路ADC缓存数据 */
extern volatile uint32_t g_last_update_time[4];     /* 四路数据更新时间 */
extern volatile uint8_t g_current_channel_index;    /* 当前通道索引 */

/* 全局调试变量 - 可在Watch窗口中观察 */
extern volatile uint8_t g_debug_current_channel;    /* 调试：当前通道 */
extern volatile uint16_t g_debug_data[4];           /* 调试：缓存数据 */
extern volatile uint32_t g_debug_timestamps[4];     /* 调试：时间戳 */
extern volatile uint32_t g_debug_data_age[4];       /* 调试：数据年龄(ms) */
extern volatile bool g_debug_i2c_ok;                /* 调试：I2C通信状态 */
extern volatile uint16_t g_debug_config_reg;        /* 调试：配置寄存器值 */
extern volatile uint32_t g_debug_update_count;      /* 调试：更新计数器 */

/* 核心功能函数 - 连续转换模式 */
bool cm1103_init(void);
bool cm1103_read_all_channels(cm1103_quadrant_data_t *quad_data);
void cm1103_update_continuous_data(void);

/* 底层寄存器操作函数 */
bool cm1103_write_register(uint8_t reg_addr, uint16_t reg_data);
bool cm1103_read_register(uint8_t reg_addr, uint16_t *reg_data);

/* 内部辅助函数 */
bool cm1103_config_channel(cm1103_channel_t channel);
bool cm1103_read_conversion(uint16_t *data);
float cm1103_adc_to_voltage(uint16_t adc_value);
bool cm1103_is_cached_data_valid(void);
void cm1103_reset_cache(void);
void cm1103_get_cache_status(void);

/* 诊断和调试函数 */
bool cm1103_test_i2c_communication(void);
bool cm1103_verify_device_presence(void);
bool cm1103_read_all_registers(uint16_t *reg_values);
void cm1103_deep_diagnosis(void);

/* 注意：由于DRDY引脚硬件上拉到3.3V，不使用中断功能 */

#ifdef __cplusplus
}
#endif

#endif /* CM1103_DRIVER_H */
