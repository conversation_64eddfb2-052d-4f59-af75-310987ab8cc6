<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>GD32F310</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32F310C8</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32F3x0_DFP.3.3.0</PackID>
          <PackURL>https://gd32mcu.com/data/documents/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x02000) IROM(0x08000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0GD32F3x0 -********** -FL010000 -FP0($$Device:GD32F310C8$Flash\GD32F3x0.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:GD32F310C8$Device\Include\gd32f3x0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:GD32F310C8$SVD\GD32F3x0.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\output\</OutputDirectory>
          <OutputName>Project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\list\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x2000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_STDPERIPH_DRIVER,GD32F3X0,GD32F310</Define>
              <Undefine></Undefine>
              <IncludePath>..\CMSIS;..\CMSIS\GD\GD32F3x0\Include;..\CMSIS\GD\GD32F3x0\Source\ARM;..\Library;..\Library\Include;..\Library\Source;..\User;..\HARDWARE;..\HARDWARE;..\HARDWARE\IIC</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\gd32f3x0_it.c</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\systick.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_gd32f3x0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\system_gd32f3x0.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Startup</GroupName>
          <Files>
            <File>
              <FileName>startup_gd32f3x0.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>gd32f350r_eval.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\gd32f350r_eval.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Doc</GroupName>
          <Files>
            <File>
              <FileName>readme.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\readme.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Library</GroupName>
          <Files>
            <File>
              <FileName>gd32f3x0_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_adc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cec.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cmp.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_crc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_ctc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_ctc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dac.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dbg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dma.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_exti.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fwdgt.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_gpio.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_i2c.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_misc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_pmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rcu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rtc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_spi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_timer.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_tsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_tsi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_usart.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_wwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_wwdgt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>HARDWARE</GroupName>
          <Files>
            <File>
              <FileName>UART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\UART.c</FilePath>
            </File>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\led.c</FilePath>
            </File>
            <File>
              <FileName>myiic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\IIC\myiic.c</FilePath>
            </File>
            <File>
              <FileName>ads1115.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\ads1115.c</FilePath>
            </File>
            <File>
              <FileName>crc16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\crc16.c</FilePath>
            </File>
            <File>
              <FileName>RS485.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\RS485.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>GD32F330</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32F330RB</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32F3x0_DFP.3.3.0</PackID>
          <PackURL>https://gd32mcu.com/data/documents/pack/</PackURL>
          <Cpu>IRAM(0x20000000-0x20003FFF)IROM(0x08000000-0x0801FFFF) CLOCK(8000000) CPUTYPE("Cortex-M4") FPU2</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"Startup\GD\GD32F3x0\startup_gd32f3x0.s" ("GD32F3x0 Startup Code")</StartupFile>
          <FlashDriverDll>UL2CM3(-O207 -S0 -C0 -FO7 -********** -FC800 -FN1 -FF0GD32F3x0 -********** -FL080000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>gd32f3x0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>SFD\GD\GD32F3x0\GD32F3x0.SFR</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>GD\GD32F3x0\</RegisterFilePath>
          <DBRegisterFilePath>GD\GD32F3x0\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\output\</OutputDirectory>
          <OutputName>Project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM3</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM3</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_STDPERIPH_DRIVER,GD32F3X0,GD32F330</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Firmware\CMSIS\GD\GD32F3x0\Include;..\..\Utilities;..\..\Firmware\CMSIS;..\..\Template;..\..\Firmware\CMSIS\GD\GD32F3x0\Source\ARM;..\..\Firmware\GD32F3x0_standard_peripheral\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\gd32f3x0_it.c</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\systick.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_gd32f3x0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\system_gd32f3x0.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Startup</GroupName>
          <Files>
            <File>
              <FileName>startup_gd32f3x0.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>gd32f350r_eval.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\gd32f350r_eval.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Doc</GroupName>
          <Files>
            <File>
              <FileName>readme.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\readme.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Library</GroupName>
          <Files>
            <File>
              <FileName>gd32f3x0_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_adc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cec.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cmp.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_crc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_ctc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_ctc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dac.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dbg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dma.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_exti.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fwdgt.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_gpio.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_i2c.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_misc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_pmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rcu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rtc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_spi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_timer.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_tsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_tsi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_usart.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_wwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_wwdgt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>HARDWARE</GroupName>
          <Files>
            <File>
              <FileName>UART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\UART.c</FilePath>
            </File>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\led.c</FilePath>
            </File>
            <File>
              <FileName>myiic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\IIC\myiic.c</FilePath>
            </File>
            <File>
              <FileName>ads1115.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\ads1115.c</FilePath>
            </File>
            <File>
              <FileName>crc16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\crc16.c</FilePath>
            </File>
            <File>
              <FileName>RS485.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\RS485.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>GD32F350</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32F350RB</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32F3x0_DFP.3.3.0</PackID>
          <PackURL>https://gd32mcu.com/data/documents/pack/</PackURL>
          <Cpu>IRAM(0x20000000-0x20003FFF)IROM(0x08000000-0x0801FFFF) CLOCK(8000000) CPUTYPE("Cortex-M4") FPU2</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"Startup\GD\GD32F3x0\startup_gd32f3x0.s" ("GD32F3x0 Startup Code")</StartupFile>
          <FlashDriverDll>UL2CM3(-O207 -S0 -C0 -FO7 -********** -FC800 -FN1 -FF0GD32F3x0 -********** -FL080000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>gd32f3x0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>SFD\GD\GD32F3x0\GD32F3x0.SFR</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>GD\GD32F3x0\</RegisterFilePath>
          <DBRegisterFilePath>GD\GD32F3x0\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\output\</OutputDirectory>
          <OutputName>Project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\list\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM3</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM3</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_STDPERIPH_DRIVER,GD32F3X0,GD32F350</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Firmware\CMSIS\GD\GD32F3x0\Include;..\..\Utilities;..\..\Firmware\CMSIS;..\..\Template;..\..\Firmware\CMSIS\GD\GD32F3x0\Source\ARM;..\..\Firmware\GD32F3x0_standard_peripheral\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\gd32f3x0_it.c</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\systick.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_gd32f3x0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\system_gd32f3x0.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Startup</GroupName>
          <Files>
            <File>
              <FileName>startup_gd32f3x0.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>gd32f350r_eval.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\gd32f350r_eval.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Doc</GroupName>
          <Files>
            <File>
              <FileName>readme.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\readme.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Library</GroupName>
          <Files>
            <File>
              <FileName>gd32f3x0_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_adc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cec.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cmp.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_crc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_ctc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_ctc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dac.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dbg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dma.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_exti.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fwdgt.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_gpio.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_i2c.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_misc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_pmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rcu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rtc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_spi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_timer.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_tsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_tsi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_usart.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_wwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_wwdgt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>HARDWARE</GroupName>
          <Files>
            <File>
              <FileName>UART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\UART.c</FilePath>
            </File>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\led.c</FilePath>
            </File>
            <File>
              <FileName>myiic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\IIC\myiic.c</FilePath>
            </File>
            <File>
              <FileName>ads1115.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\ads1115.c</FilePath>
            </File>
            <File>
              <FileName>crc16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\crc16.c</FilePath>
            </File>
            <File>
              <FileName>RS485.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\RS485.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>GD32F355</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32F310F8</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32F3x0_DFP.3.3.0</PackID>
          <PackURL>https://gd32mcu.com/data/documents/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x02000) IROM(0x08000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0GD32F3x0 -********** -FL010000 -FP0($$Device:GD32F310F8$Flash\GD32F3x0.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:GD32F310F8$Device\Include\gd32f3x0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:GD32F310F8$SVD\GD32F3x0.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\output\</OutputDirectory>
          <OutputName>Project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x2000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_STDPERIPH_DRIVER,GD32F3X0,GD32F355</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Firmware\CMSIS\GD\GD32F3x0\Include;..\..\Utilities;..\..\Firmware\CMSIS;..\..\Template;..\..\Firmware\CMSIS\GD\GD32F3x0\Source\ARM;..\..\Firmware\GD32F3x0_standard_peripheral\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\gd32f3x0_it.c</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\systick.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_gd32f3x0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\system_gd32f3x0.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Startup</GroupName>
          <Files>
            <File>
              <FileName>startup_gd32f3x0.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>gd32f350r_eval.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\gd32f350r_eval.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Doc</GroupName>
          <Files>
            <File>
              <FileName>readme.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\readme.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Library</GroupName>
          <Files>
            <File>
              <FileName>gd32f3x0_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_adc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cec.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cmp.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_crc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_ctc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_ctc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dac.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dbg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dma.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_exti.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fwdgt.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_gpio.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_i2c.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_misc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_pmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rcu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rtc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_spi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_timer.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_tsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_tsi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_usart.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_wwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_wwdgt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>HARDWARE</GroupName>
          <Files>
            <File>
              <FileName>UART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\UART.c</FilePath>
            </File>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\led.c</FilePath>
            </File>
            <File>
              <FileName>myiic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\IIC\myiic.c</FilePath>
            </File>
            <File>
              <FileName>ads1115.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\ads1115.c</FilePath>
            </File>
            <File>
              <FileName>crc16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\crc16.c</FilePath>
            </File>
            <File>
              <FileName>RS485.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\RS485.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>GD32F370</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32F310F8</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32F3x0_DFP.3.3.0</PackID>
          <PackURL>https://gd32mcu.com/data/documents/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x02000) IROM(0x08000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0GD32F3x0 -********** -FL010000 -FP0($$Device:GD32F310F8$Flash\GD32F3x0.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:GD32F310F8$Device\Include\gd32f3x0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:GD32F310F8$SVD\GD32F3x0.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\output\</OutputDirectory>
          <OutputName>Project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x2000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_STDPERIPH_DRIVER,GD32F3X0,GD32F370</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Firmware\CMSIS\GD\GD32F3x0\Include;..\..\Utilities;..\..\Firmware\CMSIS;..\..\Template;..\..\Firmware\CMSIS\GD\GD32F3x0\Source\ARM;..\..\Firmware\GD32F3x0_standard_peripheral\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\gd32f3x0_it.c</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\systick.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_gd32f3x0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\system_gd32f3x0.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Startup</GroupName>
          <Files>
            <File>
              <FileName>startup_gd32f3x0.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>gd32f350r_eval.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\gd32f350r_eval.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Doc</GroupName>
          <Files>
            <File>
              <FileName>readme.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\readme.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Library</GroupName>
          <Files>
            <File>
              <FileName>gd32f3x0_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_adc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cec.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_cmp.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_crc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_ctc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_ctc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dac.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dbg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_dma.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_exti.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_fwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_fwdgt.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_gpio.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_i2c.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_misc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_pmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rcu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_rtc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_spi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_timer.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_tsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_tsi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_usart.c</FilePath>
            </File>
            <File>
              <FileName>gd32f3x0_wwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Library\Source\gd32f3x0_wwdgt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>HARDWARE</GroupName>
          <Files>
            <File>
              <FileName>UART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\UART.c</FilePath>
            </File>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\led.c</FilePath>
            </File>
            <File>
              <FileName>myiic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\IIC\myiic.c</FilePath>
            </File>
            <File>
              <FileName>ads1115.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\ads1115.c</FilePath>
            </File>
            <File>
              <FileName>crc16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\crc16.c</FilePath>
            </File>
            <File>
              <FileName>RS485.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\RS485.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

</Project>
