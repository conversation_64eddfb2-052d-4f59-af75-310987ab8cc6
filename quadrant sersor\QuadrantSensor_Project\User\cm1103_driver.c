/*!
    \file    cm1103_driver.c
    \brief   CM1103 Quadrant Sensor Driver Implementation - Simplified Version

    \version 2025-07-31, V2.0.0, Simplified CM1103 driver for GD32F310F8P6
    \note    专注于核心功能：通过软件I2C读取四路模拟输入电压
*/

#include "cm1103_driver.h"
#include "systick.h"
#include <stdio.h>

/* 全局变量 */
volatile bool g_cm1103_data_ready = false;
volatile bool g_cm1103_conversion_done = false;

/* 数据缓存 - 连续转换模式下的数据缓存 */
volatile uint16_t g_cached_channel_data[CM1103_MAX_CHANNELS] = {0};
volatile uint32_t g_last_update_time[CM1103_MAX_CHANNELS] = {0};
volatile uint8_t g_current_channel_index = 0;

/* 全局调试变量 - 可在Watch窗口中观察 */
volatile uint8_t g_debug_current_channel = 0;
volatile uint16_t g_debug_data[4] = {0};
volatile uint32_t g_debug_timestamps[4] = {0};
volatile uint32_t g_debug_data_age[4] = {0};
volatile bool g_debug_i2c_ok = false;
volatile uint16_t g_debug_config_reg = 0;
volatile uint32_t g_debug_update_count = 0;  /* 更新计数器 */

/* 简化的配置 - 只保留必要的设置 */
static uint16_t current_config = CM1103_CONFIG_DEFAULT;
static cm1103_channel_t current_channel = CM1103_CHANNEL_A;

/*!
    \brief      initialize CM1103 sensor - simplified version
    \param[in]  none
    \param[out] none
    \retval     true: success, false: failed
*/
/*!
    \brief      scan I2C bus for devices (debugging function)
    \param[in]  none
    \param[out] none
    \retval     number of devices found
*/
uint8_t cm1103_i2c_scan(void)
{
    uint8_t devices_found = 0;
    uint16_t dummy_data;

    /* 扫描常见的I2C地址 */
    uint8_t test_addresses[] = {0x48, 0x49, 0x4A, 0x4B};

    for(int i = 0; i < 4; i++) {
        i2c_result_t result = soft_i2c_read_reg(test_addresses[i], 0x00, &dummy_data);
        if(result == I2C_OK) {
            devices_found++;
            /* 找到设备在地址 test_addresses[i] */
        }
    }

    return devices_found;
}

bool cm1103_init(void)
{
    /* 初始化软件I2C */
    soft_i2c_init();

    /* 延时等待设备稳定 - CM1103上电后需要时间稳定 */
    delay_1ms(100);

    /* 扫描I2C总线查找设备 */
    uint8_t devices = cm1103_i2c_scan();
    if(devices == 0) {
        /* 没有找到任何I2C设备 */
        return false;
    }

    /* 测试I2C通信 - 尝试读取转换寄存器验证通信 */
    uint16_t test_data;
    i2c_result_t i2c_result = soft_i2c_read_reg(CM1103_DEVICE_ADDR, CM1103_REG_CONVERSION, &test_data);
    if(i2c_result != I2C_OK) {
        /* I2C通信失败 - 记录错误类型便于调试 */
        // 错误类型: I2C_ERROR_NACK, I2C_ERROR_TIMEOUT, I2C_ERROR_BUS_BUSY
        return false;
    }

    /* 配置CM1103为连续转换模式，±4.096V量程，100 SPS
     * 注意：DRDY引脚在硬件上通过上拉电阻连接到3.3V，不使用中断功能
     * 采用轮询方式读取数据，通过延时确保转换完成
     */
    if(!cm1103_write_register(CM1103_REG_CONFIG, CM1103_CONFIG_DEFAULT)) {
        return false;
    }

    uint16_t read_config;
    if(!cm1103_read_register(CM1103_REG_CONFIG, &read_config)) {
        return false;
    }

    /* 验证关键配置位是否正确写入 */
    if((read_config & CM1103_CONFIG_PGA_MASK) != CM1103_CONFIG_PGA_4096) {
        /* PGA配置验证失败 */
        return false;
    }

    /* 验证比较器是否正确禁用 (COMP_QUE = 11) */
    if((read_config & CM1103_CONFIG_CQUE_MASK) != 0x0003) {
        /* 比较器禁用配置失败 */
        return false;
    }

    /* 初始化缓存数据和时间戳 */
    for(uint8_t i = 0; i < CM1103_MAX_CHANNELS; i++) {
        g_cached_channel_data[i] = 0;
        g_last_update_time[i] = 0;
    }

    /* 设置初始通道为A，从通道0开始 */
    g_current_channel_index = 0;
    if(!cm1103_config_channel(CM1103_CHANNEL_A)) {
        return false;
    }

    /* 延时让设备完全稳定，确保第一次转换完成 */
    delay_1ms(150);  /* 增加延时确保第一次转换完成 */

    return true;
}

/*!
    \brief      update continuous conversion data (call periodically)
    \param[in]  none
    \param[out] none
    \retval     none
    \note       修复后的缓存逻辑：
                1. 读取当前已配置通道的数据
                2. 存储到对应的通道索引
                3. 配置下一个通道，为下次读取做准备
                这样确保数据和通道索引正确匹配
*/
void cm1103_update_continuous_data(void)
{
    static uint32_t last_channel_switch = 0;
    static const uint32_t channel_switch_interval = 150; /* 增加间隔确保转换完成 */
    static uint32_t consecutive_errors = 0;  /* 连续错误计数器 */
    static const uint32_t max_consecutive_errors = 5;  /* 最大连续错误次数 */
    static uint8_t current_channel = 0;  /* 当前配置的通道（用于读取数据） */
    static uint8_t next_channel = 1;     /* 下一个要配置的通道，从1开始避免重复配置通道0 */
    static bool initialized = true;      /* 修改：初始化时已经配置了通道0，直接设为true */
    static bool first_read = true;       /* 新增：标记是否为第一次读取 */

    uint32_t current_time = systick_get_tick();

    /* 检查是否需要切换通道 */
    uint32_t time_diff = current_time - last_channel_switch;  /* 无符号减法自动处理溢出 */
    if(time_diff >= channel_switch_interval) {

        /* 读取当前已配置通道的转换数据 */
        uint16_t data;
        if(cm1103_read_conversion(&data)) {
            /* 验证读取的通道索引是否有效 */
            if(current_channel < CM1103_MAX_CHANNELS) {
                /* 存储到正确的通道索引 */
                g_cached_channel_data[current_channel] = data;
                g_last_update_time[current_channel] = current_time;

                /* 设置数据就绪标志 */
                g_cm1103_data_ready = true;

                /* 更新调试变量 */
                g_debug_data[current_channel] = data;
                g_debug_timestamps[current_channel] = current_time;

                /* 重置错误计数器 */
                consecutive_errors = 0;

                /* 数据验证：检查数据是否合理 */
                static uint16_t last_data[CM1103_MAX_CHANNELS] = {0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF};
                static uint8_t same_data_count[CM1103_MAX_CHANNELS] = {0};

                if(data == last_data[current_channel]) {
                    same_data_count[current_channel]++;
                    /* 如果连续10次读取到相同数据，可能有问题 */
                    if(same_data_count[current_channel] > 10) {
                        /* 可能的通道切换问题，但不立即报错 */
                    }
                } else {
                    same_data_count[current_channel] = 0;
                    last_data[current_channel] = data;
                }
            }
        } else {
            /* 读取失败，增加错误计数 */
            consecutive_errors++;

            /* 如果连续错误过多，重置缓存 */
            if(consecutive_errors >= max_consecutive_errors) {
                cm1103_reset_cache();
                consecutive_errors = 0;
                initialized = false;
                first_read = true;
                /* 重新配置当前通道 */
                cm1103_channel_t channels[] = {CM1103_CHANNEL_A, CM1103_CHANNEL_B,
                                               CM1103_CHANNEL_C, CM1103_CHANNEL_D};
                if(cm1103_config_channel(channels[current_channel])) {
                    initialized = true;
                }
                return; /* 错误恢复后直接返回 */
            }
        }

        /* 准备切换到下一个通道 */
        current_channel = next_channel;  /* 先更新当前通道 */
        next_channel = (next_channel + 1) % CM1103_MAX_CHANNELS;

        cm1103_channel_t channels[] = {CM1103_CHANNEL_A, CM1103_CHANNEL_B,
                                       CM1103_CHANNEL_C, CM1103_CHANNEL_D};

        /* 配置新通道，为下次读取做准备 */
        if(cm1103_config_channel(channels[current_channel])) {
            /* 更新全局通道索引 */
            g_current_channel_index = current_channel;

            if(!initialized) {
                initialized = true;
            }
        } else {
            /* 配置失败，增加错误计数 */
            consecutive_errors++;

            /* 如果通道配置连续失败，重新初始化整个系统 */
            if(consecutive_errors >= max_consecutive_errors) {
                cm1103_reset_cache();
                initialized = false;
                consecutive_errors = 0;
                first_read = true;
            }
        }

        last_channel_switch = current_time;
    }
}

/*!
    \brief      configure CM1103 channel
    \param[in]  channel: channel to configure
    \param[out] none
    \retval     true: success, false: failed
*/
bool cm1103_config_channel(cm1103_channel_t channel)
{
    uint16_t config = current_config;

    /* 清除MUX位 */
    config &= ~CM1103_CONFIG_MUX_MASK;

    /* 设置对应通道 */
    switch(channel) {
        case CM1103_CHANNEL_A:
            config |= CM1103_CONFIG_MUX_AIN0;
            break;
        case CM1103_CHANNEL_B:
            config |= CM1103_CONFIG_MUX_AIN1;
            break;
        case CM1103_CHANNEL_C:
            config |= CM1103_CONFIG_MUX_AIN2;
            break;
        case CM1103_CHANNEL_D:
            config |= CM1103_CONFIG_MUX_AIN3;
            break;
        default:
            return false;
    }

    /* 在单次转换模式下，设置OS位启动转换 */
    config |= CM1103_CONFIG_OS;  /* 设置OS位启动单次转换 */

    /* 写入配置寄存器，单次转换模式下设置OS位会启动转换 */
    if(!cm1103_write_register(CM1103_REG_CONFIG, config)) {
        /* I2C写入失败 */
        return false;
    }

    /* 验证配置是否写入成功（重要：确保通道切换正确） */
    uint16_t read_config;
    if(cm1103_read_register(CM1103_REG_CONFIG, &read_config)) {
        /* 检查MUX位是否正确设置 */
        uint16_t expected_mux = config & CM1103_CONFIG_MUX_MASK;
        uint16_t actual_mux = read_config & CM1103_CONFIG_MUX_MASK;

        if(actual_mux != expected_mux) {
            /* MUX配置验证失败 - 这是导致通道数据错误的关键问题 */
            return false;
        }

        /* 额外验证：检查其他关键配置位 */
        if((read_config & CM1103_CONFIG_PGA_MASK) != (config & CM1103_CONFIG_PGA_MASK)) {
            /* PGA配置验证失败 */
            return false;
        }
    } else {
        /* 读取配置寄存器失败 */
        return false;
    }

    current_config = config;
    current_channel = channel;

    return true;
}



/*!
    \brief      read CM1103 conversion result
    \param[in]  none
    \param[out] data: pointer to store conversion result
    \retval     true: success, false: failed
*/
bool cm1103_read_conversion(uint16_t *data)
{
    /* 单次转换模式：需要等待转换完成 */
    /* 检查OS位，当OS=0时表示转换完成 */
    uint16_t config;
    uint32_t timeout = 100; /* 100ms超时 */

    /* 等待转换完成（OS位变为0） */
    do {
        if(!cm1103_read_register(CM1103_REG_CONFIG, &config)) {
            return false; /* 读取配置寄存器失败 */
        }

        if((config & CM1103_CONFIG_OS) == 0) {
            /* 转换完成，读取结果 */
            return cm1103_read_register(CM1103_REG_CONVERSION, data);
        }

        delay_1ms(1); /* 1ms延时 */
        timeout--;

    } while(timeout > 0);

    /* 超时，转换未完成 */
    return false;
}

/*!
    \brief      convert ADC value to voltage
    \param[in]  adc_value: 16-bit ADC value
    \param[out] none
    \retval     voltage in volts
*/
float cm1103_adc_to_voltage(uint16_t adc_value)
{
    /* CM1103单端输入模式，4.096V量程，16位ADC
     * ADC值范围: 0 ~ 65535
     * 电压范围: 0V ~ 4.096V (单端输入)
     * 转换公式: voltage = adc_value * 4.096 / 65535
     * 注意：单端输入时，0V对应ADC值0，4.096V对应ADC值65535
     */
    float voltage = ((float)adc_value * 4.096f) / 65535.0f;
    return voltage;
}

/*!
    \brief      reset cached data and timestamps
    \param[in]  none
    \param[out] none
    \retval     none
*/
void cm1103_reset_cache(void)
{
    /* 重置所有缓存数据 */
    for(uint8_t i = 0; i < CM1103_MAX_CHANNELS; i++) {
        g_cached_channel_data[i] = 0;
        g_last_update_time[i] = 0;
    }

    /* 重置状态标志 */
    g_cm1103_data_ready = false;
    g_cm1103_conversion_done = false;
    g_current_channel_index = 0;
}

/*!
    \brief      get cache status for debugging
    \param[in]  none
    \param[out] none
    \retval     none
*/
void cm1103_get_cache_status(void)
{
    /* 调试函数：更新全局调试变量，可在Watch窗口中观察 */
    uint32_t current_time = systick_get_tick();

    /* 更新全局调试变量 */
    g_debug_current_channel = g_current_channel_index;

    /* 复制缓存数据到调试变量 */
    for(uint8_t i = 0; i < 4; i++) {
        g_debug_data[i] = g_cached_channel_data[i];
        g_debug_timestamps[i] = g_last_update_time[i];

        /* 计算数据年龄 */
        if(g_last_update_time[i] == 0) {
            g_debug_data_age[i] = 0xFFFFFFFF; /* 从未更新 */
        } else {
            g_debug_data_age[i] = current_time - g_last_update_time[i];
        }
    }

    /* 检查I2C通信状态 */
    uint16_t test_data;
    g_debug_i2c_ok = cm1103_read_register(CM1103_REG_CONVERSION, &test_data);

    /* 检查当前配置寄存器状态 */
    uint16_t temp_config_reg = 0;
    if(cm1103_read_register(CM1103_REG_CONFIG, &temp_config_reg)) {
        g_debug_config_reg = temp_config_reg;
    }

    /* 增加更新计数器 */
    g_debug_update_count++;

    /* 在这里设置断点，查看全局调试变量 */
}

/*!
    \brief      check if cached data is valid (not too old)
    \param[in]  none
    \param[out] none
    \retval     true: data is valid, false: data is stale
*/
bool cm1103_is_cached_data_valid(void)
{
    uint32_t current_time = systick_get_tick();
    const uint32_t max_data_age_ms = 500; /* 数据超过500ms认为过期 */

    /* 检查所有通道的数据是否都在有效期内 */
    for(uint8_t i = 0; i < CM1103_MAX_CHANNELS; i++) {
        if(g_last_update_time[i] == 0) {
            /* 通道从未更新过 */
            return false;
        }

        uint32_t data_age = current_time - g_last_update_time[i];  /* 无符号减法自动处理溢出 */
        if(data_age > max_data_age_ms) {
            /* 数据过期 */
            return false;
        }
    }

    return true;
}

/*!
    \brief      read all quadrant channels - simplified version
    \param[in]  none
    \param[out] quad_data: pointer to store quadrant data
    \retval     true: success, false: failed
*/
bool cm1103_read_all_channels(cm1103_quadrant_data_t *quad_data)
{
    /* 检查缓存数据是否有效 */
    if(!cm1103_is_cached_data_valid()) {
        quad_data->valid = false;
        return false;
    }

    /* 使用缓存的数据，无需等待 */
    quad_data->quadrant_a_raw = g_cached_channel_data[0];
    quad_data->quadrant_b_raw = g_cached_channel_data[1];
    quad_data->quadrant_c_raw = g_cached_channel_data[2];
    quad_data->quadrant_d_raw = g_cached_channel_data[3];

    /* 转换为电压值 */
    quad_data->quadrant_a_voltage = cm1103_adc_to_voltage(g_cached_channel_data[0]);
    quad_data->quadrant_b_voltage = cm1103_adc_to_voltage(g_cached_channel_data[1]);
    quad_data->quadrant_c_voltage = cm1103_adc_to_voltage(g_cached_channel_data[2]);
    quad_data->quadrant_d_voltage = cm1103_adc_to_voltage(g_cached_channel_data[3]);

    quad_data->timestamp = systick_get_tick();
    quad_data->valid = true;

    return true;
}



/* ========== 底层寄存器操作函数 ========== */

/*!
    \brief      write CM1103 register
    \param[in]  reg_addr: register address
    \param[in]  reg_data: data to write
    \param[out] none
    \retval     true: success, false: failed
*/
bool cm1103_write_register(uint8_t reg_addr, uint16_t reg_data)
{
    return (soft_i2c_write_reg(CM1103_DEVICE_ADDR, reg_addr, reg_data) == I2C_OK);
}

/*!
    \brief      read CM1103 register
    \param[in]  reg_addr: register address
    \param[out] reg_data: pointer to store read data
    \retval     true: success, false: failed
*/
bool cm1103_read_register(uint8_t reg_addr, uint16_t *reg_data)
{
    return (soft_i2c_read_reg(CM1103_DEVICE_ADDR, reg_addr, reg_data) == I2C_OK);
}

/* ========== 诊断和调试函数 ========== */

/*!
    \brief      test I2C communication with CM1103
    \param[in]  none
    \param[out] none
    \retval     true: communication OK, false: communication failed
*/
bool cm1103_test_i2c_communication(void)
{
    uint16_t test_data;

    /* 尝试读取转换寄存器 */
    i2c_result_t result = soft_i2c_read_reg(CM1103_DEVICE_ADDR, CM1103_REG_CONVERSION, &test_data);
    if(result != I2C_OK) {
        return false;
    }

    /* 尝试读取配置寄存器 */
    result = soft_i2c_read_reg(CM1103_DEVICE_ADDR, CM1103_REG_CONFIG, &test_data);
    if(result != I2C_OK) {
        return false;
    }

    return true;
}

/*!
    \brief      verify CM1103 device presence
    \param[in]  none
    \param[out] none
    \retval     true: device present, false: device not found
*/
bool cm1103_verify_device_presence(void)
{
    /* 扫描I2C总线查找CM1103设备 */
    uint16_t dummy_data;
    i2c_result_t result = soft_i2c_read_reg(CM1103_DEVICE_ADDR, CM1103_REG_CONVERSION, &dummy_data);

    return (result == I2C_OK);
}

/*!
    \brief      read all CM1103 registers for diagnosis
    \param[in]  none
    \param[out] reg_values: array to store register values [4]
    \retval     true: success, false: failed
*/
bool cm1103_read_all_registers(uint16_t *reg_values)
{
    if(reg_values == NULL) {
        return false;
    }

    /* 读取所有4个寄存器 */
    for(uint8_t i = 0; i < 4; i++) {
        if(!cm1103_read_register(i, &reg_values[i])) {
            return false;
        }
        delay_1ms(1); /* 寄存器读取间隔 */
    }

    return true;
}

/*!
    \brief      comprehensive diagnosis of CM1103 system
    \param[in]  none
    \param[out] none
    \retval     none
    \note       This function performs deep diagnosis and stores results in static variables
                that can be examined in debugger
*/
void cm1103_deep_diagnosis(void)
{
    /* 诊断结果结构体 */
    static volatile struct {
        /* I2C通信测试 */
        bool i2c_communication_ok;
        bool device_presence_ok;

        /* 寄存器读取测试 */
        bool register_read_ok;
        uint16_t all_registers[4];

        /* 通道配置测试 */
        bool channel_config_ok[4];
        uint16_t config_readback[4];

        /* 数据读取测试 */
        bool data_read_ok[4];
        uint16_t test_data[4];

        /* 缓存状态 */
        uint16_t cached_data[4];
        uint32_t cache_timestamps[4];
        uint8_t current_channel;

        /* 错误统计 */
        uint32_t i2c_error_count;
        uint32_t config_error_count;
        uint32_t data_error_count;

    } diagnosis_result = {0};

    /* 1. I2C通信测试 */
    diagnosis_result.i2c_communication_ok = cm1103_test_i2c_communication();
    diagnosis_result.device_presence_ok = cm1103_verify_device_presence();

    /* 2. 读取所有寄存器 */
    uint16_t temp_registers[4];
    diagnosis_result.register_read_ok = cm1103_read_all_registers(temp_registers);
    if(diagnosis_result.register_read_ok) {
        for(uint8_t i = 0; i < 4; i++) {
            diagnosis_result.all_registers[i] = temp_registers[i];
        }
    }

    /* 3. 测试每个通道的配置 */
    cm1103_channel_t channels[] = {CM1103_CHANNEL_A, CM1103_CHANNEL_B,
                                   CM1103_CHANNEL_C, CM1103_CHANNEL_D};

    for(uint8_t i = 0; i < 4; i++) {
        /* 配置通道 */
        diagnosis_result.channel_config_ok[i] = cm1103_config_channel(channels[i]);

        if(diagnosis_result.channel_config_ok[i]) {
            /* 读取配置寄存器验证 */
            uint16_t temp_config;
            if(cm1103_read_register(CM1103_REG_CONFIG, &temp_config)) {
                diagnosis_result.config_readback[i] = temp_config;
                delay_1ms(20); /* 等待转换完成 */

                /* 读取转换数据 */
                uint16_t temp_data;
                diagnosis_result.data_read_ok[i] = cm1103_read_conversion(&temp_data);
                if(diagnosis_result.data_read_ok[i]) {
                    diagnosis_result.test_data[i] = temp_data;
                }

                if(!diagnosis_result.data_read_ok[i]) {
                    diagnosis_result.data_error_count++;
                }
            } else {
                diagnosis_result.config_error_count++;
            }
        } else {
            diagnosis_result.config_error_count++;
        }

        delay_1ms(10); /* 通道间延时 */
    }

    /* 4. 获取当前缓存状态 */
    for(uint8_t i = 0; i < 4; i++) {
        diagnosis_result.cached_data[i] = g_cached_channel_data[i];
        diagnosis_result.cache_timestamps[i] = g_last_update_time[i];
    }
    diagnosis_result.current_channel = g_current_channel_index;

    /* 防止编译器优化掉诊断结果 */
    (void)diagnosis_result;

    /* 在这里设置断点，查看diagnosis_result的内容 */
}



/* 注意：DRDY相关函数已移除
 * 原因：硬件设计中CM1103的RDY引脚通过上拉电阻直接连接到3.3V
 * 不连接到微控制器GPIO，因此不使用DRDY中断功能
 * 改用轮询方式，通过适当的延时确保数据转换完成
 */
